"use client";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@heroui/button";

const PdfViewer = dynamic(() => import("@/components/pdfViewers"), {
  ssr: false, // Prevents hydration issues
});

export default function PdfViewerPage() {
  const [mounted, setMounted] = useState(false);
  const params = useParams();
  const router = useRouter();
  const pdfname = params.pdfname as string;

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted)
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading PDF Viewer...</p>
        </div>
      </div>
    );

  // Validate PDF name
  if (!pdfname) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">No PDF specified</h1>
          <p className="text-gray-600 mb-4">
            Please provide a PDF name in the URL.
          </p>
          <Button color="primary" onPress={() => router.push("/pdfView")}>
            Go to PDF Viewer Home
          </Button>
        </div>
      </div>
    );
  }

  // Construct the PDF URL from the dynamic route parameter
  let decodedPdfName = decodeURIComponent(pdfname);

  // Add .pdf extension if not present
  if (!decodedPdfName.toLowerCase().endsWith(".pdf")) {
    decodedPdfName += ".pdf";
  }

  const pdfUrl = `/pdf/${decodedPdfName}`;

  return (
    <div>
      <div className="p-4">
        <div className="mb-4">
          <Button
            variant="light"
            onPress={() => router.back()}
            className="mb-2"
          >
            ← Back
          </Button>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
            {decodedPdfName}
          </h1>
        </div>
        <PdfViewer
          // fileUrl={pdfUrl}
          fileUrl="/pdf/MVP Digimeet _ Phase 1.pdf"
        />
      </div>
    </div>
  );
}
