"use client";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";

export default function PdfViewerIndexPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return <div>Loading...</div>;

  return (
    <div className="container mx-auto p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center">PDF Viewer</h1>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">How to use:</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            To view a PDF, navigate to:{" "}
            <code className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
              /pdfView/[pdfname]
            </code>
          </p>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Where{" "}
            <code className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
              [pdfname]
            </code>{" "}
            is the name of your PDF file (with or without .pdf extension).
          </p>

          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              Examples:
            </h3>
            <ul className="space-y-2 text-blue-700 dark:text-blue-300">
              <li>
                • <code>/pdfView/document.pdf</code>
              </li>
              <li>
                • <code>/pdfView/Reading%20a%20Factsheet.pdf</code>
              </li>
              <li>
                • <code>/pdfView/MVP%20Digimeet%20_%20Phase%201.pdf</code>
              </li>
            </ul>
          </div>
        </div>

        <div className="text-center">
          <h3 className="text-xl font-semibold mb-4">
            Quick Access to Sample PDFs:
          </h3>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              as={Link}
              href="/pdfView/Reading%20a%20Factsheet.pdf"
              color="primary"
              variant="solid"
            >
              View Reading a Factsheet
            </Button>
            <Button
              as={Link}
              href="/pdfView/MVP%20Digimeet%20_%20Phase%201.pdf"
              color="secondary"
              variant="solid"
            >
              View MVP Digimeet Phase 1
            </Button>
          </div>
        </div>

        <div className="mt-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Make sure your PDF files are placed in the{" "}
            <code className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
              /public/pdf/
            </code>{" "}
            directory.
          </p>
        </div>
      </div>
    </div>
  );
}
